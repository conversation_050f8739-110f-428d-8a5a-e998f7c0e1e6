#ifndef __AUTO_ENGINE_ERROR_HPP__
#define __AUTO_ENGINE_ERROR_HPP__

namespace AutoSDK {

// 错误码定义
enum EngineError {
  ENGINE_SUCCESS = 0, // 成功

  // 通用错误 (-1 ~ -99)
  ENGINE_ERROR = -1,               // 一般错误
  ENGINE_INVALID_PARAM = -2,       // 无效参数
  ENGINE_NULL_PTR = -3,            // 空指针
  ENGINE_OUT_OF_MEMORY = -4,       // 内存不足
  ENGINE_NOT_INITIALIZED = -5,     // 未初始化
  ENGINE_ALREADY_INITIALIZED = -6, // 重复初始化
  ENGINE_UNSUPPORTED_ENGINE = -7,  // 不支持的引擎类型

  // 模型相关错误 (-100 ~ -199)
  ENGINE_MODEL_LOAD_FAILED = -100,   // 模型加载失败
  ENGINE_MODEL_NOT_FOUND = -101,     // 模型文件不存在
  ENGINE_MODEL_INVALID = -102,       // 模型文件无效
  ENGINE_MODEL_VERSION_ERROR = -103, // 模型版本不匹配
  ENGINE_MODEL_FORMAT_ERROR = -104,  // 模型格式错误

  // 推理相关错误 (-200 ~ -299)
  ENGINE_INFERENCE_FAILED = -200, // 推理失败
  ENGINE_INPUT_ERROR = -201,      // 输入数据错误
  ENGINE_OUTPUT_ERROR = -202,     // 输出数据错误
  ENGINE_SHAPE_MISMATCH = -203,   // 数据形状不匹配
  ENGINE_TYPE_MISMATCH = -204,    // 数据类型不匹配
  ENGINE_NAME_NOT_FOUND = -205,   // 节点名称未找到

  // 后端相关错误 (-300 ~ -399)
  ENGINE_BACKEND_ERROR = -300,        // 后端错误
  ENGINE_PPL3_ERROR = -301,           // PPL3 错误
  ENGINE_ONNX_ERROR = -302,           // ONNX 错误
  ENGINE_QNN_ERROR = -303,            // QNN 错误
  ENGINE_DEVICE_ERROR = -304,         // 设备错误
  ENGINE_MEMORY_ERROR = -305,         // 内存操作错误
  ENGINE_SNPE_ERROR = -306,           // SNPE 错误
  ENGINE_NCNN_ERROR = -307,           // NCNN 错误
  ENGINE_TENSORRT_ERROR = -308,       // TensorRT 错误
  ENGINE_CUDA_ERROR = -309,           // CUDA 错误
  ENGINE_NEUROPILOT_ERROR = -310,     // NeuroPilot 错误
  ENGINE_INVALID_MEMORY_SIZE = -311,  // 无效的内存大小
  ENGINE_UNSUPPORTED_DATATYPE = -312, // 不支持的数据类型

  // 配置相关错误 (-400 ~ -499)
  ENGINE_CONFIG_ERROR = -400,       // 配置错误
  ENGINE_CONFIG_NOT_FOUND = -401,   // 配置文件不存在
  ENGINE_CONFIG_PARSE_ERROR = -402, // 配置解析错误
  ENGINE_CONFIG_INVALID = -403,     // 配置无效
};

// 错误码转字符串
inline const char *GetErrorString(int error_code) {
  switch (error_code) {
  case ENGINE_SUCCESS:
    return "Success";
  case ENGINE_ERROR:
    return "General error";
  case ENGINE_INVALID_PARAM:
    return "Invalid parameter";
  case ENGINE_NULL_PTR:
    return "Null pointer";
  case ENGINE_OUT_OF_MEMORY:
    return "Out of memory";
  case ENGINE_NOT_INITIALIZED:
    return "Not initialized";
  case ENGINE_ALREADY_INITIALIZED:
    return "Already initialized";
  case ENGINE_UNSUPPORTED_ENGINE:
    return "Unsupported engine type";
  case ENGINE_MODEL_LOAD_FAILED:
    return "Model load failed";
  case ENGINE_MODEL_NOT_FOUND:
    return "Model file not found";
  case ENGINE_MODEL_INVALID:
    return "Invalid model file";
  case ENGINE_MODEL_VERSION_ERROR:
    return "Model version mismatch";
  case ENGINE_MODEL_FORMAT_ERROR:
    return "Model format error";
  case ENGINE_INFERENCE_FAILED:
    return "Inference failed";
  case ENGINE_INPUT_ERROR:
    return "Input data error";
  case ENGINE_OUTPUT_ERROR:
    return "Output data error";
  case ENGINE_SHAPE_MISMATCH:
    return "Shape mismatch";
  case ENGINE_TYPE_MISMATCH:
    return "Data type mismatch";
  case ENGINE_NAME_NOT_FOUND:
    return "Node name not found";
  case ENGINE_BACKEND_ERROR:
    return "Backend error";
  case ENGINE_PPL3_ERROR:
    return "PPL3 error";
  case ENGINE_ONNX_ERROR:
    return "ONNX error";
  case ENGINE_QNN_ERROR:
    return "QNN error";
  case ENGINE_DEVICE_ERROR:
    return "Device error";
  case ENGINE_MEMORY_ERROR:
    return "Memory operation error";
  case ENGINE_SNPE_ERROR:
    return "SNPE error";
  case ENGINE_NCNN_ERROR:
    return "NCNN error";
  case ENGINE_TENSORRT_ERROR:
    return "TensorRT error";
  case ENGINE_CUDA_ERROR:
    return "CUDA error";
  case ENGINE_NEUROPILOT_ERROR:
    return "NeuroPilot error";
  case ENGINE_INVALID_MEMORY_SIZE:
    return "Invalid memory size";
  case ENGINE_UNSUPPORTED_DATATYPE:
    return "Unsupported data type";
  case ENGINE_CONFIG_ERROR:
    return "Configuration error";
  case ENGINE_CONFIG_NOT_FOUND:
    return "Configuration file not found";
  case ENGINE_CONFIG_PARSE_ERROR:
    return "Configuration parse error";
  case ENGINE_CONFIG_INVALID:
    return "Invalid configuration";
  default:
    return "Unknown error";
  }
}

} // namespace AutoSDK

#endif // __AUTO_ENGINE_ERROR_HPP__