#ifndef __AUTO_ENGINE_COMMON_TYPES_HPP__
#define __AUTO_ENGINE_COMMON_TYPES_HPP__

#include <string>
#include <vector>

namespace AutoSDK {
// 内存类型枚举
enum class EngineMemoryType { MEM_ON_CPU = 0, MEM_ON_DEVICE };

// 引擎类型枚举
enum class EngineType {
  ENGINE_TYPE_UNKNOWN = 0,
  ENGINE_TYPE_PPL3,
  ENGINE_TYPE_PPL3_DSP,
  ENGINE_TYPE_PPL3_OCL,
  ENGINE_TYPE_PPL3_CUDA,
  ENGINE_TYPE_QNN,
  ENGINE_TYPE_ONNX,
  ENGINE_TYPE_SNPE,       // 添加SNPE引擎类型
  ENGINE_TYPE_RKNN,       // 添加 RKNN 引擎类型
  ENGINE_TYPE_NCNN,       // 添加 NCNN 引擎类型
  ENGINE_TYPE_TENSORRT,   // 添加 TensorRT 引擎类型
  ENGINE_TYPE_SDNN,       // 添加 SDNN 引擎类型
  ENGINE_TYPE_MNN,        // 添加 MNN 引擎类型
  ENGINE_TYPE_NEUROPILOT, // 添加 NeuroPilot 引擎类型
};

enum class ModelType {
  MODEL_TYPE_UNKNOWN,
  MODEL_TYPE_FP32,
  MODEL_TYPE_FP16,
  MODEL_TYPE_INT8,
  MODEL_TYPE_INT4
};
enum class ModelDataLayOut {
  MODEL_DATA_LAYOUT_UNKNOWN,
  MODEL_DATA_LAYOUT_NCHW,
  MODEL_DATA_LAYOUT_NHWC
};

// 数据类型枚举
enum DataType {
  DATA_TYPE_UNKNOWN = 0,
  DATA_TYPE_FLOAT32,
  DATA_TYPE_FLOAT16,
  DATA_TYPE_INT32,
  DATA_TYPE_INT8,
  DATA_TYPE_UINT8,
  DATA_TYPE_BOOL
};

// 数据信息结构体
struct DataInfo {
  std::string name;
  std::vector<int64_t> dims;
  size_t size = 0;
  ModelDataLayOut data_layout = ModelDataLayOut::MODEL_DATA_LAYOUT_NCHW;
  DataType data_type = DataType::DATA_TYPE_FLOAT32; // 默认为float32类型
};

// 内存描述结构体
struct EngineData {
  void *data; // 数据指针（可以是 Host 或 Device 内存）
  EngineMemoryType type = EngineMemoryType::MEM_ON_CPU; // 内存类型
  int device_id = 1;  // 设备 ID（用于多设备场景）
  DataInfo data_info; // 数据信息
};

// 模型信息结构体
struct ModelInfo {
  std::string model_name;
  std::string engine_type;
  std::string model_type;
  std::string device_type;
  struct TensorInfo {
    std::string name;
    std::string data_type;
    std::string data_layout;
    std::vector<int> shape;
  };

  std::vector<TensorInfo> input_info;
  std::vector<TensorInfo> output_info;
};

} // namespace AutoSDK

#endif // __AUTO_ENGINE_COMMON_TYPES_HPP__