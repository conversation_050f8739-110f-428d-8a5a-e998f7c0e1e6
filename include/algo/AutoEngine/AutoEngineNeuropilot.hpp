#ifndef __AUTO_ENGINE_NEUROPILOT_HPP__
#define __AUTO_ENGINE_NEUROPILOT_HPP__

#include "AutoEngineBase.hpp"
#include "engine_common/engine_error.hpp"
#include "engine_common/engine_types.hpp"
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <vector>

// 只在启用NeuroPilot引擎时编译
#if CONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT

// 包含NeuroPilot头文件
#include "neuron/api/NeuronAdapter.h"
#include "neuron/api/RuntimeAPI.h"

namespace AutoSDK {

class AutoEngineNeuropilot : public AutoEngineBase {
public:
  AutoEngineNeuropilot();
  ~AutoEngineNeuropilot() override;

  // 禁止拷贝
  AutoEngineNeuropilot(const AutoEngineNeuropilot &) = delete;
  AutoEngineNeuropilot &operator=(const AutoEngineNeuropilot &) = delete;

  // 接口实现
  int Init(const std::vector<char> &model_data) override;
  int Release() override;
  int Forward() override;
  int GetBlobData(std::vector<EngineData> &outputs_data) override;
  int SetBlobData(std::vector<EngineData> &input_datas) override;
  int GetInputsInfo(std::vector<DataInfo> &input_infos) override;
  int GetOutputsInfo(std::vector<DataInfo> &output_infos) override;

private:
  // 辅助方法
  int LoadModelFromMemory(const std::vector<char> &model_data);
  int InitializeInputOutputInfo();
  int UpdateInputInfo();
  int UpdateOutputInfo();
  int ConvertToNeuroPilotTensor(const EngineData &engine_data, void* buffer, size_t buffer_size);
  int ConvertFromNeuroPilotTensor(void* buffer, size_t buffer_size, EngineData &engine_data, const std::string &name);
  DataType ConvertNeuroPilotTypeToEngineType(int32_t neuron_type);
  int32_t ConvertEngineTypeToNeuroPilotType(DataType engine_type);
  
  // NeuroPilot相关成员
  void* runtime_{nullptr};                // NeuroPilot运行时
  NeuronModel* model_{nullptr};           // NeuroPilot模型
  NeuronCompilation* compilation_{nullptr}; // NeuroPilot编译
  NeuronExecution* execution_{nullptr};   // NeuroPilot执行

  // 输入输出信息
  std::vector<std::string> input_names_;
  std::vector<std::string> output_names_;
  std::map<std::string, DataInfo> input_info_map_;
  std::map<std::string, DataInfo> output_info_map_;
  std::map<std::string, uint32_t> input_indices_;
  std::map<std::string, uint32_t> output_indices_;
  
  // 输入输出缓冲区
  std::map<std::string, std::vector<uint8_t>> input_buffers_;
  std::map<std::string, std::vector<uint8_t>> output_buffers_;
  
  // 线程安全
  std::mutex data_mutex_;
  
  // 初始化标志
  bool initialized_{false};
};

} // namespace AutoSDK

#endif // CONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT

#endif // __AUTO_ENGINE_NEUROPILOT_HPP__
