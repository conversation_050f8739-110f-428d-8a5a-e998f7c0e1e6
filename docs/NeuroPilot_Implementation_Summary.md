# MTK NeuroPilot Engine 实现总结

## 实现概述

本次实现为 auto-engine 项目添加了对 MTK NeuroPilot 7.x 的支持，使其能够在 MediaTek 芯片的 APU (AI Processing Unit) 上进行高效的深度学习推理。

## 实现的文件和功能

### 1. 核心引擎类型定义
- **文件**: `include/algo/AutoEngine/engine_common/engine_types.hpp`
- **修改**: 添加 `ENGINE_TYPE_NEUROPILOT` 枚举值
- **文件**: `include/algo/AutoEngine/engine_common/engine_error.hpp`
- **修改**: 添加 `ENGINE_NEUROPILOT_ERROR (-310)` 错误码和对应的错误描述

### 2. NeuroPilot 引擎头文件
- **文件**: `include/algo/AutoEngine/AutoEngineNeuropilot.hpp`
- **功能**: 
  - 定义 `AutoEngineNeuropilot` 类接口
  - 继承自 `AutoEngineBase`
  - 包含 NeuroPilot API 头文件
  - 定义私有成员变量和辅助方法

### 3. NeuroPilot 引擎实现
- **文件**: `src/algo/AutoEngine/AutoEngineNeuropilot.cpp`
- **功能**:
  - 实现所有虚函数：`Init`, `Release`, `Forward`, `SetBlobData`, `GetBlobData`, `GetInputsInfo`, `GetOutputsInfo`
  - NeuroPilot 运行时环境创建和管理
  - 模型加载和推理执行
  - 输入输出数据转换和缓冲区管理
  - 线程安全保护

### 4. 引擎适配器更新
- **文件**: `src/algo/AutoEngine/AutoEngineAdapter.cpp`
- **修改**:
  - 添加 `#include "AutoEngineNeuropilot.hpp"`
  - 在引擎创建 switch 语句中添加 NeuroPilot 分支
  - 在 `DetectEngineType()` 中添加 "NEUROPILOT" 类型检测
  - 在 `PrintEngineSupportStatus()` 中添加 NeuroPilot 状态显示

### 5. CMake 配置文件
- **文件**: `3dpart/cmake/neuropilot.cmake`
- **功能**:
  - 配置 NeuroPilot 头文件和库文件路径
  - 设置编译宏 `CONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT=1`
  - 检查必要的库文件存在性
  - 配置安装规则

### 6. 主 CMake 配置更新
- **文件**: `3dpart/cmake/common.cmake`
- **修改**: 添加 neuropilot.cmake 包含和相关宏调用
- **文件**: `CMakeLists.txt`
- **修改**: 添加 `${NEUROPILOT_LIB}` 到链接库列表

### 7. 构建配置
- **文件**: `CppBuild.toml`
- **修改**: 添加 `[option.enable_neuropilot]` 配置选项

### 8. 文档和测试
- **文件**: `docs/NeuroPilot_Engine_Guide.md` - 用户使用指南
- **文件**: `docs/NeuroPilot_Implementation_Summary.md` - 实现总结
- **文件**: `scripts/test_neuropilot_build.sh` - 编译测试脚本

## 技术特性

### 支持的功能
1. **模型加载**: 支持从内存加载 DLA 格式模型
2. **推理执行**: 使用 NeuroPilot Runtime API 进行推理
3. **数据类型**: 支持 FP32、FP16、INT8、UINT8 数据类型
4. **数据布局**: 支持 NCHW 和 NHWC 布局
5. **内存管理**: 自动管理输入输出缓冲区
6. **线程安全**: 使用互斥锁保证多线程安全
7. **错误处理**: 完整的错误码和异常处理

### 性能优化
1. **内部缓冲区**: 预分配输入输出缓冲区，减少动态内存分配
2. **零拷贝**: 尽可能减少数据拷贝操作
3. **非ION缓冲区**: 当前使用标准内存，未来可扩展支持 ION 缓冲区

## 使用方法

### 编译启用
```bash
# Android 平台编译
source /mnt/nfs/profile
TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild dep --cfg="enable_neuropilot"
TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild build --build-type=release --install
```

### 代码使用
```cpp
#include "AutoEngineAdapter.hpp"

AutoEngineAdapter engine;
engine.Init("model.tar");  // 模型配置中 engine_type: "NEUROPILOT"
engine.SetBlobData(input_datas);
engine.Forward();
engine.GetBlobData(output_datas);
```

## 依赖库

### 必需的 NeuroPilot 库
- `libneuron_adapter.so` - 核心适配器
- `libneuron_runtime.so` - 运行时库
- `libmvpu_*.so` - MVPU 相关库

### 头文件
- `neuron/api/NeuronAdapter.h` - 主要 API
- `neuron/api/RuntimeAPI.h` - 运行时 API
- `neuron/api/Types.h` - 类型定义

## 限制和注意事项

1. **平台限制**: 仅支持 Android 平台的 MTK 芯片
2. **模型格式**: 需要 MTK 编译器生成的 DLA 格式
3. **版本要求**: NeuroPilot 7.x 版本
4. **内存类型**: 当前使用 CPU 内存，未来可扩展 ION 支持

## 测试验证

1. **编译测试**: 使用 `scripts/test_neuropilot_build.sh` 验证编译
2. **功能测试**: 使用现有的 `test/test_engine.cpp` 进行功能验证
3. **性能测试**: 可使用 `test/test_engine_perf.cpp` 进行性能测试

## 未来改进方向

1. **ION 缓冲区支持**: 实现零拷贝优化
2. **动态形状**: 支持运行时改变输入尺寸
3. **批处理**: 支持批量推理
4. **量化支持**: 扩展更多量化格式
5. **异步推理**: 支持异步执行模式

## 总结

MTK NeuroPilot Engine 的实现遵循了 auto-engine 项目的设计模式，提供了完整的功能支持和良好的扩展性。实现注重功能正确性，为后续的性能优化奠定了坚实基础。
