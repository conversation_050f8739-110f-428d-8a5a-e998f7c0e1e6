# MTK NeuroPilot Engine 使用指南

## 概述

MTK NeuroPilot Engine 是为 MediaTek 平台优化的深度学习推理引擎，支持在 MTK 芯片的 APU (AI Processing Unit) 上进行高效的神经网络推理。

## 支持的平台

- **Android**: 支持 MTK 6897 及兼容平台
- **架构**: aarch64 (ARM64)
- **设备**: 需要支持 NeuroPilot 7.x 的 MTK 芯片

## 依赖库

NeuroPilot 引擎依赖以下库文件（位于 `3dpart/plateform/neuropilot7/mt6897/lib/`）：

### 核心库
- `libneuron_adapter.so` - NeuroPilot 适配器库
- `libneuron_runtime.so` - NeuroPilot 运行时库

### MVPU 支持库
- `libmvpu_engine_25.so` - MVPU 引擎
- `libmvpu_runtime_25.so` - MVPU 运行时
- `libmvpu_pattern_25.so` - MVPU 模式库
- 其他 MVPU 相关库

## 编译配置

### 1. 启用 NeuroPilot 引擎

在编译时使用 `enable_neuropilot` 配置选项：

```bash
# Android 平台编译
source /mnt/nfs/profile
TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild dep --cfg="enable_neuropilot"
TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild build --build-type=release --install
```

### 2. CMake 配置

NeuroPilot 引擎会自动配置以下内容：
- 头文件路径：`3dpart/plateform/neuropilot7/mt6897/include/`
- 库文件路径：`3dpart/plateform/neuropilot7/mt6897/lib/`
- 编译宏：`CONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT=1`

## 模型格式

NeuroPilot 引擎支持以下模型格式：
- **DLA 文件**: MediaTek 编译器生成的 `.dla` 格式
- **模型配置**: 需要在模型配置文件中指定 `engine_type: "NEUROPILOT"`

### 模型配置示例

```json
{
  "model_name": "example_model",
  "engine_type": "NEUROPILOT",
  "model_type": "FP16",
  "device_type": "APU",
  "input_info": [
    {
      "name": "input",
      "data_type": "float32",
      "data_layout": "NHWC",
      "shape": [1, 224, 224, 3]
    }
  ],
  "output_info": [
    {
      "name": "output",
      "data_type": "float32", 
      "data_layout": "NHWC",
      "shape": [1, 1000]
    }
  ]
}
```

## 使用方法

### 1. 基本使用

```cpp
#include "AutoEngineAdapter.hpp"

using namespace AutoSDK;

// 创建引擎实例
AutoEngineAdapter engine;

// 初始化引擎，加载模型
if (engine.Init("path/to/model.tar") != 0) {
    return -1;
}

// 设置输入数据并执行推理
std::vector<EngineData> input_datas;
// ... 准备输入数据 ...

if (engine.SetBlobData(input_datas) != 0 || engine.Forward() != 0) {
    return -1;
}

// 获取输出结果
std::vector<EngineData> output_datas;
if (engine.GetBlobData(output_datas) != 0) {
    return -1;
}
```

### 2. 性能优化建议

- **内存管理**: NeuroPilot 引擎使用内部缓冲区管理，减少内存拷贝
- **数据格式**: 推荐使用 NHWC 数据布局以获得最佳性能
- **精度**: 支持 FP32、FP16 和 INT8 量化模型
- **并发**: 引擎内部使用互斥锁保证线程安全

## 错误处理

NeuroPilot 引擎定义了专用的错误码：
- `ENGINE_NEUROPILOT_ERROR (-310)`: NeuroPilot 特定错误

常见错误及解决方法：
1. **模型加载失败**: 检查 DLA 文件格式和版本兼容性
2. **推理失败**: 检查输入数据格式和大小
3. **内存不足**: 检查设备可用内存

## 限制和注意事项

1. **平台限制**: 仅支持 Android 平台的 MTK 芯片
2. **模型格式**: 需要使用 MTK 编译器生成的 DLA 格式
3. **版本兼容**: NeuroPilot 7.x 版本
4. **内存类型**: 当前实现使用 CPU 内存，未来可扩展支持 ION 缓冲区

## 调试和日志

引擎提供详细的日志输出，包括：
- 初始化过程
- 模型加载状态
- 推理时间统计
- 错误信息

使用 `LOG_INFO`、`LOG_ERROR` 等宏查看详细日志。

## 未来改进

1. **ION 缓冲区支持**: 减少内存拷贝，提升性能
2. **动态形状**: 支持动态输入尺寸
3. **批处理**: 支持批量推理
4. **更多数据类型**: 扩展支持更多精度格式
