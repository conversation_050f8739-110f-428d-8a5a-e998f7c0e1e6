#!/bin/bash

# MTK NeuroPilot Engine 编译测试脚本

echo "=== MTK NeuroPilot Engine Build Test ==="

# 设置环境
source /mnt/nfs/profile

# 测试 Android 平台编译（NeuroPilot 仅支持 Android）
echo "Testing NeuroPilot engine compilation for Android platform..."

# 1. 依赖安装
echo "Step 1: Installing dependencies..."
TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild dep --cfg="enable_neuropilot"

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies for NeuroPilot"
    exit 1
fi

# 2. 编译
echo "Step 2: Building with NeuroPilot engine..."
TARGET_OS=android TARGET_ARCH=aarch64 TARGET_CPU_TYPE=clang-r18b cppbuild build --build-type=release --install

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to build with NeuroPilot engine"
    exit 1
fi

echo "SUCCESS: NeuroPilot engine compilation completed successfully!"

# 3. 检查生成的库文件
echo "Step 3: Checking generated library files..."
INSTALL_DIR="target/android-aarch64/release"

if [ -f "${INSTALL_DIR}/lib_static/auto_engine/libauto_engine.a" ]; then
    echo "✓ Found auto_engine static library"
else
    echo "✗ auto_engine static library not found"
fi

# 4. 检查 NeuroPilot 相关库是否被正确链接
echo "Step 4: Checking NeuroPilot library dependencies..."
if [ -d "${INSTALL_DIR}/lib_static/auto_engine/" ]; then
    echo "Library directory contents:"
    ls -la "${INSTALL_DIR}/lib_static/auto_engine/"
else
    echo "Library directory not found"
fi

echo "=== Build Test Completed ==="
