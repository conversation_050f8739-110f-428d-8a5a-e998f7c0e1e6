cmake_minimum_required(VERSION 3.12)
project(auto_engine)

include(cppbuild_common)  # must be called after project() command
CPPBUILD_DEPEND()

# 编译选项设置
set(CMAKE_EXPORT_COMPILE_COMMANDS TRUE)
set(CMAKE_BUILD_TYPE "Release")
set(CMAKE_CXX_STANDARD 14)
add_definitions(-fPIC -DSDK_EXPORTS)
add_compile_options(-Wno-sign-compare -Wno-unused-variable -Wno-unused-result)

# GCC 7.5 ABI compatibility fix
# if(CPPBUILD_TARGET_CPU_TYPE STREQUAL "gcc7_5")
#     # Use old ABI to match OpenCV and other libraries
#     add_definitions(-D_GLIBCXX_USE_CXX11_ABI=0)
#     message(STATUS "auto_engine: Using old C++ ABI for GCC 7.5 compatibility")
# endif()

# 设置项目的头文件目录
set(AUTO_ENGINE_INCLUDE_DIRS_EXPORT
    ${CMAKE_CURRENT_SOURCE_DIR}/include/algo/AutoEngine
    ${CMAKE_CURRENT_SOURCE_DIR}/include/algo/AutoEngine/engine_common
    ${CMAKE_CURRENT_SOURCE_DIR}/include/utils
    ${CMAKE_CURRENT_SOURCE_DIR}/include/common
)
set(AUTO_ENGINE_INCLUDE_DIRS
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/include/algo
    ${CMAKE_CURRENT_SOURCE_DIR}/include/algo/AutoEngine
    ${CMAKE_CURRENT_SOURCE_DIR}/include/algo/AutoEngine/engine_common
    ${CMAKE_CURRENT_SOURCE_DIR}/include/algo/AutoEngine/utils
    ${CMAKE_CURRENT_SOURCE_DIR}/include/utils/ad_descrypt
    ${AUTO_ENGINE_INCLUDE_DIRS_EXPORT}
)

# 将头文件目录添加到项目中
include_directories(${AUTO_ENGINE_INCLUDE_DIRS})

# 如果是作为子项目被引入，也要导出这些包含路径
if(NOT CMAKE_SOURCE_DIR STREQUAL CMAKE_CURRENT_SOURCE_DIR)
    set(AUTO_ENGINE_INCLUDE_DIRS ${AUTO_ENGINE_INCLUDE_DIRS} PARENT_SCOPE)
endif()

# 引入平台相关配置
include(${CMAKE_CURRENT_SOURCE_DIR}/3dpart/cmake/common.cmake)

# 初始化平台变量
INIT_PLATEFORM_VARS()

# 为不同的目标系统设置额外库
if(CPPBUILD_TARGET_OS STREQUAL "ANDROID")
    set(EXTRA_LIB log android)
endif()

# 源文件
set(__src_list)
file(GLOB_RECURSE __src_list
    ${PROJECT_SOURCE_DIR}/src/algo/**/*.cpp
    ${PROJECT_SOURCE_DIR}/src/utils/*.cpp
    ${PROJECT_SOURCE_DIR}/src/utils/ad_descrypt/*.c
)

# 配置所有平台
CONFIG_ALL_PLATEFORMS()

# 设置目标名称和依赖
SET(TARGET_NAME auto_engine)
SET(DST_INC_DIR include_static/${TARGET_NAME})
SET(DST_LIB_DIR lib_static/${TARGET_NAME})
SET(AUTOENGINE_TARGET_DEPS)

# 构建静态库
CPPBUILD_ADD_TARGET(${TARGET_NAME}
    DEPS ${AUTOENGINE_TARGET_DEPS}
    STATIC
    KEEPNAME
    LIBS_API_VISIBLE
    ${__src_list}
)

# 配置NCNN的OpenMP
CONFIG_NCNN_OPENMP()

# 确保PPL3依赖关系被正确传递
target_link_libraries(${TARGET_NAME} PUBLIC
    PPL3Runtime_static
    PPL3CV_static
    PPL3Core_static
    ${ONNX_LIB}
    ${EXTRA_LIB}
    ${SNPE_LIB}
    ${RKNN_LIB}
    ${NCNN_LIB}
    ${TENSORRT_LIB}
    ${CUDART_LIB}
    ${SDNN_LIB}
    ${MNN_LIB}
    ${NEUROPILOT_LIB}
)

# 添加库的搜索路径
target_link_directories(${TARGET_NAME} PUBLIC
    ${PPL3_PATH}/lib
)

# 为Android平台添加SNPE库路径
if(CPPBUILD_TARGET_OS STREQUAL "ANDROID" AND CONFIG_AUTO_ENGINE_ENABLE_SNPE)
    target_link_directories(${TARGET_NAME} PUBLIC
        ${SNPE_PATH}/lib/aarch64-android
    )
endif()

CPPBUILD_TARGET_INCLUDE_DIRECTORIES(${TARGET_NAME} PUBLIC ${AUTO_ENGINE_INCLUDE_DIRS_EXPORT})
TARGET_COMPILE_DEFINITIONS(${TARGET_NAME} PRIVATE MATRIX_EXPORTS)

# 测试
# 为Linux x86_64平台总是编译测试（用于开发和调试）
IF(CPPBUILD_IS_TOPLEVEL AND (NOT CPPBUILD_REMOTE))
    add_subdirectory(test)
ENDIF()

# 安装
if(CPPBUILD_IS_TOPLEVEL OR (NOT CPPBUILD_REMOTE))
    # 头文件
    CPPBUILD_INSTALL(DIRECTORY ${PROJECT_SOURCE_DIR}/include/
                     DESTINATION ${DST_INC_DIR}
                     FILES_MATCHING PATTERN "*.hpp" PATTERN "*.h")
    CPPBUILD_INSTALL(FILES ${PROJECT_SOURCE_DIR}/include/common/common.h DESTINATION include/${TARGET_NAME})

    # 库文件
    CPPBUILD_INSTALL(TARGETS ${TARGET_NAME} DESTINATION ${DST_LIB_DIR})

    # 安装所有平台的文件
    INSTALL_ALL_PLATEFORMS()

    IF(CPPBUILD_IS_TOPLEVEL AND (NOT CPPBUILD_REMOTE))
        # 脚本和测试资源
        CPPBUILD_INSTALL(FILES ${PROJECT_SOURCE_DIR}/scripts/env.sh DESTINATION .)

        # 精度测试
        CPPBUILD_INSTALL(DIRECTORY ${PROJECT_SOURCE_DIR}/test/android_aarch64 DESTINATION test/)
        CPPBUILD_INSTALL(DIRECTORY ${PROJECT_SOURCE_DIR}/test/linux_x86_64 DESTINATION test/)
    ENDIF()
endif()
CPPBUILD_END()
