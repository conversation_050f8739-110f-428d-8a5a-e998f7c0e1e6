[package]
name = "auto_engine"
version = "1.3.0"
authors = ["laiqinglong <<EMAIL>>"]

[exports]
libs = ["auto_engine"]

[option.build_base]
default = false

[option.enable_ppl3]
default = true

[option.enable_ppl3_dsp]
default = false

[option.enable_ppl3_ocl]
default = false

[option.enable_snpe]
default = false

[option.enable_qnn]
default = false

[option.enable_onnx]
default = false

[option.enable_rknn]
default = false

[option.enable_ncnn]
default = false 

[option.enable_ppl3_cuda]
default = false

[option.enable_tensorrt]
default = false

[option.enable_sdnn]
default = false

[option.enable_mnn]
default = false

[option.enable_neuropilot]
default = false