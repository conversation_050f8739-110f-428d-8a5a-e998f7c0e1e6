#include "AutoEngineAdapter.hpp"
#include "AutoEngineBase.hpp"
#include "AutoEngineMnn.hpp"
#include "AutoEngineNcnn.hpp"
#include "AutoEngineNeuropilot.hpp"
#include "AutoEngineOnnx.hpp"
#include "AutoEnginePPL3.hpp"
#include "AutoEnginePPL3Cuda.hpp"
#include "AutoEnginePPL3Dsp.hpp"
#include "AutoEnginePPL3Ocl.hpp"
#include "AutoEngineQnn.hpp"
#include "AutoEngineRknn.hpp"
#include "AutoEngineSdnn.hpp"
#include "AutoEngineSdnnTvm.hpp"
#include "AutoEngineSnpe.hpp"
#include "AutoEngineTensorRT.hpp"
#include "log/logger.h"
#include "utils/engine_utils.hpp"
#include <algorithm> // 添加这一行以支持 std::find_if

// 重新定义日志宏，避免与TVM冲突
#undef LOG_INFO
#undef LOG_ERROR
#undef LOG_WARNING
#undef LOG_DEBUG
#define LOG_INFO(msg, ...)                                                     \
  AutoLogger::getInstance().log(AutoLogger::INFO, __FILE__, __LINE__, msg,     \
                                ##__VA_ARGS__)
#define LOG_ERROR(msg, ...)                                                    \
  AutoLogger::getInstance().log(AutoLogger::ERROR, __FILE__, __LINE__, msg,    \
                                ##__VA_ARGS__)
#define LOG_WARNING(msg, ...)                                                  \
  AutoLogger::getInstance().log(AutoLogger::WARNING, __FILE__, __LINE__, msg,  \
                                ##__VA_ARGS__)
#define LOG_DEBUG(msg, ...)                                                    \
  AutoLogger::getInstance().log(AutoLogger::DEBUG, __FILE__, __LINE__, msg,    \
                                ##__VA_ARGS__)
namespace AutoSDK {
AutoEngineAdapter::AutoEngineAdapter() = default;
AutoEngineAdapter::~AutoEngineAdapter() {
  if (engine_) {
    engine_->Release();
    engine_.reset();
  }
};

int AutoEngineAdapter::Init(const std::string &model_path) {
  // 检查参数
  if (model_path.empty()) {
    LOG_ERROR("Model path is empty");
    return ENGINE_INVALID_PARAM;
  }
  auto model_md5_str = EngineUtils::getFileMD5ByCommand(model_path);
  LOG_INFO("Model PATH:[", model_path, "] MD5[", model_md5_str, "]");
  // 解密模型文件
  model_data_ = EngineUtils::decrypt_model(model_path);
  if (model_data_.empty()) {
    LOG_ERROR("Failed to decrypt model file: ", model_path);
    return ENGINE_MODEL_LOAD_FAILED;
  }

  // 解压模型包
  auto files =
      EngineUtils::extractTarFromMemory(model_data_.data(), model_data_.size());
  if (files.empty()) {
    LOG_ERROR("Failed to extract model package");
    return ENGINE_MODEL_LOAD_FAILED;
  }

  // 解析配置文件
  std::string json_content;
  std::vector<char> model_content;
  for (const auto &file : files) {
    if (file.name == "model.config") {
      json_content = std::string(file.data.begin(), file.data.end());
    }
    LOG_INFO("file.name:", file.name, " size:", file.data.size());
  }

  if (json_content.empty()) {
    LOG_ERROR("Missing config.json in package");
    return ENGINE_MODEL_INVALID;
  }

  // 解析模型信息
  model_info_ = EngineUtils::parseJsonToModelInfo(json_content);
  if (model_info_.model_name.empty()) {
    LOG_ERROR("Failed to parse model info");
    return ENGINE_CONFIG_PARSE_ERROR;
  }
  EngineUtils::printModelInfo(model_info_);

  // 获取模型文件
  for (const auto &file : files) {
    if (file.name == model_info_.model_name) {
      model_data_ = file.data;
      break;
    }
  }

  if (model_data_.empty()) {
    LOG_ERROR("Model file not found: ", model_info_.model_name);
    return ENGINE_MODEL_LOAD_FAILED;
  }

  // 检测引擎类型
  engine_type_ = DetectEngineType();
  if (engine_type_ == EngineType::ENGINE_TYPE_UNKNOWN) {
    LOG_ERROR("Unknown engine type: ", model_info_.engine_type);
    return ENGINE_UNSUPPORTED_ENGINE;
  }
  PrintEngineSupportStatus();
  // 创建对应的引擎实例
  switch (engine_type_) {
#if CONFIG_AUTO_ENGINE_ENABLE_PPL3
  case EngineType::ENGINE_TYPE_PPL3:
    engine_ = std::make_shared<AutoEnginePPL3>();
    break;
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_PPL3_DSP
  case EngineType::ENGINE_TYPE_PPL3_DSP:
    engine_ = std::make_shared<AutoEnginePPL3Dsp>();
    break;
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_ONNX
  case EngineType::ENGINE_TYPE_ONNX:
    engine_ = std::make_shared<AutoEngineOnnx>();
    break;
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_QNN
  case EngineType::ENGINE_TYPE_QNN:
    engine_ = std::make_shared<AutoEngineQnn>();
    break;
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_SNPE
  case EngineType::ENGINE_TYPE_SNPE:
    engine_ = std::make_shared<AutoEngineSnpe>();
    break;
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_PPL3_OCL
  case EngineType::ENGINE_TYPE_PPL3_OCL:
    engine_ = std::make_shared<AutoEnginePPL3Ocl>();
    break;
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_PPL3_CUDA
  case EngineType::ENGINE_TYPE_PPL3_CUDA:
    engine_ = std::make_shared<AutoEnginePPL3Cuda>();
    break;
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_RKNN
  case EngineType::ENGINE_TYPE_RKNN:
    engine_ = std::make_shared<AutoEngineRknn>();
    break;
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_NCNN
  case EngineType::ENGINE_TYPE_NCNN:
    engine_ = std::make_shared<AutoEngineNcnn>();
    break;
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_TENSORRT
  case EngineType::ENGINE_TYPE_TENSORRT:
    engine_ = std::make_shared<AutoEngineTensorRT>();
    break;
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_SDNN
  case EngineType::ENGINE_TYPE_SDNN:
    engine_ = std::make_shared<AutoEngineSdnn>();
    break;
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_MNN
  case EngineType::ENGINE_TYPE_MNN:
    engine_ = std::make_shared<AutoEngineMnn>();
    break;
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT
  case EngineType::ENGINE_TYPE_NEUROPILOT:
    engine_ = std::make_shared<AutoEngineNeuropilot>();
    break;
#endif
  default:
    LOG_ERROR("Unsupported engine type: ", static_cast<int>(engine_type_));
    return ENGINE_UNSUPPORTED_ENGINE;
  }
  engine_->model_info_ = model_info_;
  // 初始化引擎
  int ret = engine_->Init(model_data_);
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("Engine initialization failed: ", GetErrorString(ret));
    return ret;
  }

  // 如果是NCNN引擎，调用UpdateInputOutputInfo更新输入输出信息
  if (engine_type_ == EngineType::ENGINE_TYPE_NCNN) {
    // 为NCNN引擎类型添加更新输入输出信息的特殊处理
    // 注意：这里不使用dynamic_cast，因为它需要RTTI，可能在一些平台上不支持
    // 直接调用AutoEngineBase::UpdateInputOutputInfoForNCNN方法
    ret = engine_->UpdateInputOutputInfoForNCNN();
    if (ret != ENGINE_SUCCESS) {
      LOG_ERROR("Failed to update input/output info for NCNN engine: ",
                GetErrorString(ret));
      return ret;
    }
  }

  ret = engine_->GetInputsInfo(input_infos_);
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("Failed to get input info before zero-fill forward: ",
              GetErrorString(ret));
    return ret;
  }

  ret = PreProcess();
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("PreProcess failed: ", GetErrorString(ret));
    return ret;
  }

  ret = engine_->GetOutputsInfo(output_infos_);
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("Failed to get output info: ", GetErrorString(ret));
    return ret;
  }
  //打印input_infos_
  for (const auto &input_info : input_infos_) {
    LOG_INFO("Engine Input info: ", input_info.name);
    for (const auto &dim : input_info.dims) {
      LOG_INFO("dim: ", dim);
    }
  }
  //打印output_infos_
  for (const auto &output_info : output_infos_) {
    LOG_INFO("Engine Output info: ", output_info.name);
    for (const auto &dim : output_info.dims) {
      LOG_INFO("dim: ", dim);
    }
  }
  // 验证输入shape
  if (model_info_.input_info.size() != input_infos_.size()) {
    LOG_ERROR("Input shape count mismatch: model_info has ",
              model_info_.input_info.size(), " inputs, but network has ",
              input_infos_.size(), " inputs");
    return ENGINE_SHAPE_MISMATCH;
  }

  // 验证每个输入的shape
  // 遍历每个网络输入
  for (auto &network_input : input_infos_) {
    // 在model_info中查找对应名称的输入
    auto model_input_it = std::find_if(
        model_info_.input_info.begin(), model_info_.input_info.end(),
        [&](const auto &model_input) {
          return model_input.name == network_input.name;
        });

    if (model_input_it == model_info_.input_info.end()) {
      LOG_ERROR("Input ", network_input.name, " not found in model_info");
      return ENGINE_SHAPE_MISMATCH;
    }

    const auto &model_shape = model_input_it->shape;
    const auto &network_shape = network_input.dims;
    network_input.data_layout =
        EngineUtils::stringToModelDataLayout(model_input_it->data_layout);
    if (model_shape.size() != network_shape.size()) {
      LOG_ERROR("Input ", network_input.name,
                " shape dimension mismatch: ", "model_info expects ",
                model_shape.size(), " dimensions, but network has ",
                network_shape.size(), " dimensions");
      return ENGINE_SHAPE_MISMATCH;
    }

    for (size_t j = 0; j < model_shape.size(); j++) {
      if (model_shape[j] != network_shape[j]) {
        LOG_ERROR("Input ", network_input.name, " shape mismatch at dimension ",
                  j, ": model_info expects ", model_shape[j],
                  ", but network has ", network_shape[j]);
        return ENGINE_SHAPE_MISMATCH;
      }
    }
  }

  // 验证输出shape
  if (model_info_.output_info.size() != output_infos_.size()) {
    LOG_ERROR("Output shape count mismatch: model_info has ",
              model_info_.output_info.size(), " outputs, but network has ",
              output_infos_.size(), " outputs");
    return ENGINE_SHAPE_MISMATCH;
  }

  // 验证每个输出的shape
  // 遍历每个网络输出
  for (auto &network_output : output_infos_) {
    // 在model_info中查找对应名称的输出
    auto model_output_it = std::find_if(
        model_info_.output_info.begin(), model_info_.output_info.end(),
        [&](const auto &model_output) {
          return model_output.name == network_output.name;
        });

    if (model_output_it == model_info_.output_info.end()) {
      LOG_ERROR("Output ", network_output.name, " not found in model_info");
      return ENGINE_SHAPE_MISMATCH;
    }

    const auto &model_shape = model_output_it->shape;
    const auto &network_shape = network_output.dims;
    network_output.data_layout =
        EngineUtils::stringToModelDataLayout(model_output_it->data_layout);
    if (model_shape.size() != network_shape.size()) {
      LOG_ERROR("Output ", network_output.name,
                " shape dimension mismatch: ", "model_info expects ",
                model_shape.size(), " dimensions, but network has ",
                network_shape.size(), " dimensions");
      return ENGINE_SHAPE_MISMATCH;
    }

    // 对于NCNN引擎，使用更宽松的验证策略
    if (engine_type_ == EngineType::ENGINE_TYPE_NCNN) {
      // 计算model_info中期望的总元素数
      int64_t model_total_elements =
          std::accumulate(model_shape.begin(), model_shape.end(), 1,
                          std::multiplies<int64_t>());
      // 计算网络实际输出的总元素数
      int64_t network_total_elements =
          std::accumulate(network_shape.begin(), network_shape.end(), 1,
                          std::multiplies<int64_t>());

      // 对于NCNN，如果维度数量匹配，我们接受实际的网络输出shape
      // 这是因为NCNN的输出shape可能与模型配置文件中的不完全一致
      LOG_INFO("NCNN output ", network_output.name,
               " validation: ", "model_info shape ",
               EngineUtils::dimsToString(std::vector<int64_t>(
                   model_shape.begin(), model_shape.end())),
               " (", model_total_elements, " elements) vs network shape ",
               EngineUtils::dimsToString(network_shape), " (",
               network_total_elements, " elements)");

      if (model_total_elements != network_total_elements) {
        LOG_WARNING(
            "NCNN output ", network_output.name,
            " shape differs from model_info, using actual network shape");
      }

      // 对于NCNN，我们信任实际的网络输出shape，不进行严格验证
      LOG_INFO("Using actual NCNN network output shape for ",
               network_output.name);
    } else {
      // 对于其他引擎，保持严格的shape验证
      for (size_t j = 0; j < model_shape.size(); j++) {
        if (model_shape[j] != network_shape[j]) {
          LOG_ERROR("Output ", network_output.name,
                    " shape mismatch at dimension ", j, ": model_info expects ",
                    model_shape[j], ", but network has ", network_shape[j]);
          return ENGINE_SHAPE_MISMATCH;
        }
      }
    }
  }

  return ENGINE_SUCCESS;
}
int AutoEngineAdapter::PreProcess() {
  if (!engine_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }
  // 准备零填充输入数据并执行一次前向推理
  std::vector<EngineData> input_datas;
  input_datas.resize(input_infos_.size());

  for (size_t i = 0; i < input_infos_.size(); ++i) {
    input_datas[i].data_info = input_infos_[i];
    input_datas[i].type = EngineMemoryType::MEM_ON_CPU;
    input_datas[i].data = new float[input_infos_[i].size](); // 零值初始化
  }

  int ret = engine_->SetBlobData(input_datas);
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("SetBlobData failed for zero-fill forward: ",
              GetErrorString(ret));
    for (auto &input_data : input_datas) {
      delete[] static_cast<float *>(input_data.data);
    }
    return ret;
  }
  ret = engine_->Forward();
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("Forward failed after init (zero-fill): ", GetErrorString(ret));
    for (auto &input_data : input_datas) {
      delete[] static_cast<float *>(input_data.data);
    }
    return ret;
  }

  // 清理零填充输入数据内存
  for (auto &input_data : input_datas) {
    delete[] static_cast<float *>(input_data.data);
  }
  input_datas.clear();
  std::vector<EngineData> output_datas;
  ret = GetBlobData(output_datas);
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("GetBlobData failed: ", GetErrorString(ret));
    return ret;
  }
  return ENGINE_SUCCESS;
}
int AutoEngineAdapter::Release() {
  if (!engine_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  int ret = engine_->Release();
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("Engine release failed: ", GetErrorString(ret));
    return ret;
  }

  engine_.reset();
  model_data_.clear();
  return ENGINE_SUCCESS;
}

int AutoEngineAdapter::Forward() {
  if (!engine_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  int ret = engine_->Forward();
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("Forward failed: ", GetErrorString(ret));
    return ret;
  }

  return ENGINE_SUCCESS;
}

int AutoEngineAdapter::SetBlobData(std::vector<EngineData> &input_datas) {
  if (!engine_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  int ret = engine_->SetBlobData(input_datas);
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("SetBlobData failed: ", GetErrorString(ret));
    return ret;
  }

  return ENGINE_SUCCESS;
}

int AutoEngineAdapter::GetBlobData(std::vector<EngineData> &output_datas) {
  if (!engine_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  int ret = engine_->GetBlobData(output_datas);
  if (ret != ENGINE_SUCCESS) {
    LOG_ERROR("GetBlobData failed: ", GetErrorString(ret));
    return ret;
  }

  return ENGINE_SUCCESS;
}

int AutoEngineAdapter::GetInputsInfo(std::vector<DataInfo> &input_infos) {
  if (!engine_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }
  input_infos.assign(input_infos_.begin(), input_infos_.end());
  return ENGINE_SUCCESS;
}

int AutoEngineAdapter::GetOutputsInfo(std::vector<DataInfo> &output_infos) {
  if (!engine_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }
  output_infos.assign(output_infos_.begin(), output_infos_.end());

  return ENGINE_SUCCESS;
}

EngineType AutoEngineAdapter::DetectEngineType() {
  LOG_INFO("engine_type:", model_info_.engine_type);
  if (model_info_.engine_type == "PPL3") {
    return EngineType::ENGINE_TYPE_PPL3;
  } else if (model_info_.engine_type == "PPL3_DSP") {
    return EngineType::ENGINE_TYPE_PPL3_DSP;
  } else if (model_info_.engine_type == "ONNX") {
    return EngineType::ENGINE_TYPE_ONNX;
  } else if (model_info_.engine_type == "QNN") {
    return EngineType::ENGINE_TYPE_QNN;
  } else if (model_info_.engine_type == "SNPE") {
    return EngineType::ENGINE_TYPE_SNPE;
  } else if (model_info_.engine_type == "PPL3_OCL") {
    return EngineType::ENGINE_TYPE_PPL3_OCL;
  } else if (model_info_.engine_type == "RKNN") {
    return EngineType::ENGINE_TYPE_RKNN;
  } else if (model_info_.engine_type == "PPL3_CUDA") {
    return EngineType::ENGINE_TYPE_PPL3_CUDA;
  } else if (model_info_.engine_type == "NCNN") {
    return EngineType::ENGINE_TYPE_NCNN;
  } else if (model_info_.engine_type == "TENSORRT") {
    return EngineType::ENGINE_TYPE_TENSORRT;
  } else if (model_info_.engine_type == "MNN") {
    return EngineType::ENGINE_TYPE_MNN;
  } else if (model_info_.engine_type == "NEUROPILOT") {
    return EngineType::ENGINE_TYPE_NEUROPILOT;
  }
  return EngineType::ENGINE_TYPE_UNKNOWN;
}
void AutoEngineAdapter::PrintEngineSupportStatus() {
  std::string enabled_engines = "Enabled engines: ";
  std::string disabled_engines = "Disabled engines: ";

#if CONFIG_AUTO_ENGINE_ENABLE_PPL3
  enabled_engines += "PPL3 "; // PPL3 总是启用
#else
  disabled_engines += "PPL3 ";
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_PPL3_DSP
  enabled_engines += "PPL3_DSP ";
#else
  disabled_engines += "PPL3_DSP ";
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_ONNX
  enabled_engines += "ONNX ";
#else
  disabled_engines += "ONNX ";
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_QNN
  enabled_engines += "QNN ";
#else
  disabled_engines += "QNN ";
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_SNPE
  enabled_engines += "SNPE ";
#else
  disabled_engines += "SNPE ";
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_PPL3_OCL
  enabled_engines += "PPL3_OCL ";
#else
  disabled_engines += "PPL3_OCL ";
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_PPL3_CUDA
  enabled_engines += "PPL3_CUDA ";
#else
  disabled_engines += "PPL3_CUDA ";
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_RKNN
  enabled_engines += "RKNN ";
#else
  disabled_engines += "RKNN ";
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_NCNN
  enabled_engines += "NCNN ";
#else
  disabled_engines += "NCNN ";
#endif

#if CONFIG_AUTO_ENGINE_ENABLE_TENSORRT
  enabled_engines += "TENSORRT ";
#else
  disabled_engines += "TENSORRT ";
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_SDNN
  enabled_engines += "SDNN ";
#else
  disabled_engines += "SDNN ";
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_MNN
  enabled_engines += "MNN ";
#else
  disabled_engines += "MNN ";
#endif
#if CONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT
  enabled_engines += "NEUROPILOT ";
#else
  disabled_engines += "NEUROPILOT ";
#endif

  LOG_INFO(enabled_engines.c_str());
  LOG_INFO(disabled_engines.c_str());
}
} // namespace AutoSDK