#include "AutoEngineNeuropilot.hpp"

// 只在启用NeuroPilot引擎时编译
#if CONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT

#include "log/logger.h"
#include "utils/engine_utils.hpp"
#include <algorithm>
#include <cstring>

namespace AutoSDK {

AutoEngineNeuropilot::AutoEngineNeuropilot() {
  LOG_INFO("Creating NeuroPilot engine instance");
}

AutoEngineNeuropilot::~AutoEngineNeuropilot() {
  Release();
  LOG_INFO("NeuroPilot engine instance destroyed");
}

int AutoEngineNeuropilot::Init(const std::vector<char> &model_data) {
  std::lock_guard<std::mutex> lock(data_mutex_);

  if (initialized_) {
    LOG_ERROR("NeuroPilot engine already initialized");
    return ENGINE_ALREADY_INITIALIZED;
  }

  try {
    LOG_INFO("Initializing NeuroPilot engine");

    // 创建NeuroPilot运行时环境
    EnvOptions env_options = {};
    env_options.deviceKind = kEnvOptHardware; // 使用硬件加速
    env_options.CPUThreadNum = 4;
    env_options.suppressInputConversion = false;
    env_options.suppressOutputConversion = false;

    int ret = NeuronRuntime_create(&env_options, &runtime_);
    if (ret != 0 || !runtime_) {
      LOG_ERROR("Failed to create NeuroPilot runtime, error code: ", ret);
      return ENGINE_NEUROPILOT_ERROR;
    }

    // 从内存加载模型
    ret = LoadModelFromMemory(model_data);
    if (ret != ENGINE_SUCCESS) {
      LOG_ERROR("Failed to load NeuroPilot model from memory");
      return ret;
    }

    // 初始化输入输出信息
    ret = InitializeInputOutputInfo();
    if (ret != ENGINE_SUCCESS) {
      LOG_ERROR("Failed to initialize input/output info");
      return ret;
    }

    initialized_ = true;
    LOG_INFO("NeuroPilot engine initialized successfully");
    return ENGINE_SUCCESS;

  } catch (const std::exception &e) {
    LOG_ERROR("NeuroPilot initialization failed with exception: ", e.what());
    return ENGINE_NEUROPILOT_ERROR;
  }
}

int AutoEngineNeuropilot::LoadModelFromMemory(
    const std::vector<char> &model_data) {
  try {
    // 使用NeuroPilot Runtime API从内存加载模型
    int ret = NeuronRuntime_loadNetworkFromBuffer(runtime_, model_data.data(),
                                                  model_data.size());
    if (ret != 0) {
      LOG_ERROR("Failed to load network from buffer, error code: ", ret);
      return ENGINE_MODEL_LOAD_FAILED;
    }

    LOG_INFO("NeuroPilot model loaded from memory successfully");
    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("LoadModelFromMemory failed with exception: ", e.what());
    return ENGINE_MODEL_LOAD_FAILED;
  }
}

int AutoEngineNeuropilot::InitializeInputOutputInfo() {
  try {
    // 从model_info_获取输入输出信息
    input_names_.clear();
    output_names_.clear();
    input_info_map_.clear();
    output_info_map_.clear();
    input_indices_.clear();
    output_indices_.clear();

    // 处理输入信息
    for (size_t i = 0; i < model_info_.input_info.size(); i++) {
      const auto &input_tensor = model_info_.input_info[i];

      DataInfo data_info;
      data_info.name = input_tensor.name;

      // 转换shape
      data_info.dims.clear();
      for (int dim : input_tensor.shape) {
        data_info.dims.push_back(static_cast<int64_t>(dim));
      }

      // 转换数据类型
      if (input_tensor.data_type == "float32") {
        data_info.data_type = DataType::DATA_TYPE_FLOAT32;
      } else if (input_tensor.data_type == "float16") {
        data_info.data_type = DataType::DATA_TYPE_FLOAT16;
      } else if (input_tensor.data_type == "int8") {
        data_info.data_type = DataType::DATA_TYPE_INT8;
      } else if (input_tensor.data_type == "uint8") {
        data_info.data_type = DataType::DATA_TYPE_UINT8;
      } else {
        LOG_WARNING("Unknown input data type: ", input_tensor.data_type,
                    ", using float32");
        data_info.data_type = DataType::DATA_TYPE_FLOAT32;
      }

      // 转换数据布局
      if (input_tensor.data_layout == "NCHW") {
        data_info.data_layout = ModelDataLayOut::MODEL_DATA_LAYOUT_NCHW;
      } else if (input_tensor.data_layout == "NHWC") {
        data_info.data_layout = ModelDataLayOut::MODEL_DATA_LAYOUT_NHWC;
      } else {
        LOG_WARNING("Unknown input data layout: ", input_tensor.data_layout,
                    ", using NCHW");
        data_info.data_layout = ModelDataLayOut::MODEL_DATA_LAYOUT_NCHW;
      }

      // 计算数据大小
      size_t element_count = 1;
      for (int64_t dim : data_info.dims) {
        element_count *= dim;
      }

      size_t element_size = 4; // 默认float32
      if (data_info.data_type == DataType::DATA_TYPE_FLOAT16) {
        element_size = 2;
      } else if (data_info.data_type == DataType::DATA_TYPE_INT8 ||
                 data_info.data_type == DataType::DATA_TYPE_UINT8) {
        element_size = 1;
      }

      data_info.size = element_count * element_size;

      input_names_.push_back(data_info.name);
      input_info_map_[data_info.name] = data_info;
      input_indices_[data_info.name] = static_cast<uint32_t>(i);

      // 预分配输入缓冲区
      input_buffers_[data_info.name].resize(data_info.size);
    }

    // 处理输出信息（类似输入处理）
    for (size_t i = 0; i < model_info_.output_info.size(); i++) {
      const auto &output_tensor = model_info_.output_info[i];

      DataInfo data_info;
      data_info.name = output_tensor.name;

      // 转换shape
      data_info.dims.clear();
      for (int dim : output_tensor.shape) {
        data_info.dims.push_back(static_cast<int64_t>(dim));
      }

      // 转换数据类型
      if (output_tensor.data_type == "float32") {
        data_info.data_type = DataType::DATA_TYPE_FLOAT32;
      } else if (output_tensor.data_type == "float16") {
        data_info.data_type = DataType::DATA_TYPE_FLOAT16;
      } else if (output_tensor.data_type == "int8") {
        data_info.data_type = DataType::DATA_TYPE_INT8;
      } else if (output_tensor.data_type == "uint8") {
        data_info.data_type = DataType::DATA_TYPE_UINT8;
      } else {
        LOG_WARNING("Unknown output data type: ", output_tensor.data_type,
                    ", using float32");
        data_info.data_type = DataType::DATA_TYPE_FLOAT32;
      }

      // 转换数据布局
      if (output_tensor.data_layout == "NCHW") {
        data_info.data_layout = ModelDataLayOut::MODEL_DATA_LAYOUT_NCHW;
      } else if (output_tensor.data_layout == "NHWC") {
        data_info.data_layout = ModelDataLayOut::MODEL_DATA_LAYOUT_NHWC;
      } else {
        LOG_WARNING("Unknown output data layout: ", output_tensor.data_layout,
                    ", using NCHW");
        data_info.data_layout = ModelDataLayOut::MODEL_DATA_LAYOUT_NCHW;
      }

      // 计算数据大小
      size_t element_count = 1;
      for (int64_t dim : data_info.dims) {
        element_count *= dim;
      }

      size_t element_size = 4; // 默认float32
      if (data_info.data_type == DataType::DATA_TYPE_FLOAT16) {
        element_size = 2;
      } else if (data_info.data_type == DataType::DATA_TYPE_INT8 ||
                 data_info.data_type == DataType::DATA_TYPE_UINT8) {
        element_size = 1;
      }

      data_info.size = element_count * element_size;

      output_names_.push_back(data_info.name);
      output_info_map_[data_info.name] = data_info;
      output_indices_[data_info.name] = static_cast<uint32_t>(i);

      // 预分配输出缓冲区
      output_buffers_[data_info.name].resize(data_info.size);
    }

    LOG_INFO("Initialized ", input_names_.size(), " inputs and ",
             output_names_.size(), " outputs");
    return ENGINE_SUCCESS;

  } catch (const std::exception &e) {
    LOG_ERROR("InitializeInputOutputInfo failed with exception: ", e.what());
    return ENGINE_NEUROPILOT_ERROR;
  }
}

int AutoEngineNeuropilot::Release() {
  std::lock_guard<std::mutex> lock(data_mutex_);

  try {
    if (runtime_) {
      // 释放NeuroPilot运行时
      NeuronRuntime_release(runtime_);
      runtime_ = nullptr;
    }

    // 清理内部数据
    input_names_.clear();
    output_names_.clear();
    input_info_map_.clear();
    output_info_map_.clear();
    input_indices_.clear();
    output_indices_.clear();
    input_buffers_.clear();
    output_buffers_.clear();

    initialized_ = false;
    LOG_INFO("NeuroPilot engine released successfully");
    return ENGINE_SUCCESS;

  } catch (const std::exception &e) {
    LOG_ERROR("NeuroPilot release failed with exception: ", e.what());
    return ENGINE_NEUROPILOT_ERROR;
  }
}

int AutoEngineNeuropilot::Forward() {
  std::lock_guard<std::mutex> lock(data_mutex_);

  if (!initialized_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  try {
    double start = EngineUtils::getTimeOfMSeconds();

    // 设置输入数据到NeuroPilot运行时
    for (size_t i = 0; i < input_names_.size(); i++) {
      const std::string &input_name = input_names_[i];
      auto it = input_buffers_.find(input_name);
      if (it == input_buffers_.end() || it->second.empty()) {
        LOG_ERROR("Input data not found for: ", input_name);
        return ENGINE_INPUT_ERROR;
      }

      uint64_t handle = static_cast<uint64_t>(i);
      BufferAttribute attr = {NON_ION_FD}; // 使用非ION缓冲区

      int ret = NeuronRuntime_setInput(runtime_, handle, it->second.data(),
                                       it->second.size(), attr);
      if (ret != 0) {
        LOG_ERROR("Failed to set input data for: ", input_name,
                  " error: ", ret);
        return ENGINE_INPUT_ERROR;
      }
    }

    // 设置输出缓冲区到NeuroPilot运行时
    for (size_t i = 0; i < output_names_.size(); i++) {
      const std::string &output_name = output_names_[i];
      auto it = output_buffers_.find(output_name);
      if (it == output_buffers_.end()) {
        LOG_ERROR("Output buffer not found for: ", output_name);
        return ENGINE_OUTPUT_ERROR;
      }

      uint64_t handle = static_cast<uint64_t>(i);
      BufferAttribute attr = {NON_ION_FD}; // 使用非ION缓冲区

      int ret = NeuronRuntime_setOutput(runtime_, handle, it->second.data(),
                                        it->second.size(), attr);
      if (ret != 0) {
        LOG_ERROR("Failed to set output buffer for: ", output_name,
                  " error: ", ret);
        return ENGINE_OUTPUT_ERROR;
      }
    }

    // 执行推理
    int ret = NeuronRuntime_inference(runtime_);
    if (ret != 0) {
      LOG_ERROR("NeuroPilot inference failed with error code: ", ret);
      return ENGINE_INFERENCE_FAILED;
    }

    double end = EngineUtils::getTimeOfMSeconds();
    LOG_INFO("NeuroPilot inference time: ", end - start, " ms");

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("Forward failed with exception: ", e.what());
    return ENGINE_INFERENCE_FAILED;
  }
}

int AutoEngineNeuropilot::SetBlobData(std::vector<EngineData> &input_datas) {
  std::lock_guard<std::mutex> lock(data_mutex_);

  if (!initialized_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  try {
    for (auto &input_data : input_datas) {
      std::string input_name = input_data.data_info.name;

      auto it = input_info_map_.find(input_name);
      if (it == input_info_map_.end()) {
        LOG_ERROR("Input name not found: ", input_name);
        return ENGINE_NAME_NOT_FOUND;
      }

      const DataInfo &expected_info = it->second;

      // 验证数据大小
      if (input_data.data_info.size != expected_info.size) {
        LOG_ERROR("Input data size mismatch for ", input_name, ": expected ",
                  expected_info.size, ", got ", input_data.data_info.size);
        return ENGINE_SHAPE_MISMATCH;
      }

      // 复制数据到内部缓冲区
      auto buffer_it = input_buffers_.find(input_name);
      if (buffer_it == input_buffers_.end()) {
        LOG_ERROR("Input buffer not found for: ", input_name);
        return ENGINE_INPUT_ERROR;
      }

      if (input_data.data && input_data.data_info.size > 0) {
        std::memcpy(buffer_it->second.data(), input_data.data,
                    input_data.data_info.size);
        LOG_DEBUG("Set input data for: ", input_name,
                  " size: ", input_data.data_info.size);
      } else {
        LOG_ERROR("Invalid input data for: ", input_name);
        return ENGINE_INPUT_ERROR;
      }
    }

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("SetBlobData failed with exception: ", e.what());
    return ENGINE_INPUT_ERROR;
  }
}

int AutoEngineNeuropilot::GetBlobData(std::vector<EngineData> &outputs_data) {
  std::lock_guard<std::mutex> lock(data_mutex_);

  if (!initialized_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  try {
    outputs_data.clear();
    outputs_data.reserve(output_names_.size());

    for (const std::string &output_name : output_names_) {
      auto info_it = output_info_map_.find(output_name);
      auto buffer_it = output_buffers_.find(output_name);

      if (info_it == output_info_map_.end() ||
          buffer_it == output_buffers_.end()) {
        LOG_ERROR("Output info or buffer not found for: ", output_name);
        return ENGINE_OUTPUT_ERROR;
      }

      const DataInfo &output_info = info_it->second;
      const std::vector<uint8_t> &output_buffer = buffer_it->second;

      EngineData engine_data;
      engine_data.data_info = output_info;
      engine_data.type = EngineMemoryType::MEM_ON_CPU;
      engine_data.device_id = 1;

      // 分配输出数据内存
      engine_data.data = new uint8_t[output_info.size];
      if (!engine_data.data) {
        LOG_ERROR("Failed to allocate memory for output: ", output_name);
        return ENGINE_OUT_OF_MEMORY;
      }

      // 复制数据
      std::memcpy(engine_data.data, output_buffer.data(), output_info.size);

      outputs_data.push_back(engine_data);
      LOG_DEBUG("Got output data for: ", output_name,
                " size: ", output_info.size);
    }

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("GetBlobData failed with exception: ", e.what());
    return ENGINE_OUTPUT_ERROR;
  }
}

int AutoEngineNeuropilot::GetInputsInfo(std::vector<DataInfo> &input_infos) {
  std::lock_guard<std::mutex> lock(data_mutex_);

  if (!initialized_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  try {
    input_infos.clear();
    input_infos.reserve(input_names_.size());

    for (const std::string &input_name : input_names_) {
      auto it = input_info_map_.find(input_name);
      if (it != input_info_map_.end()) {
        input_infos.push_back(it->second);
      }
    }

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("GetInputsInfo failed with exception: ", e.what());
    return ENGINE_ERROR;
  }
}

int AutoEngineNeuropilot::GetOutputsInfo(std::vector<DataInfo> &output_infos) {
  std::lock_guard<std::mutex> lock(data_mutex_);

  if (!initialized_) {
    LOG_ERROR("Engine not initialized");
    return ENGINE_NOT_INITIALIZED;
  }

  try {
    output_infos.clear();
    output_infos.reserve(output_names_.size());

    for (const std::string &output_name : output_names_) {
      auto it = output_info_map_.find(output_name);
      if (it != output_info_map_.end()) {
        output_infos.push_back(it->second);
      }
    }

    return ENGINE_SUCCESS;
  } catch (const std::exception &e) {
    LOG_ERROR("GetOutputsInfo failed with exception: ", e.what());
    return ENGINE_ERROR;
  }
}

} // namespace AutoSDK

#endif // CONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT
