/**
 * \file
 * Misc Neuron Runtime API
 * ---
 * Miscellaneous functionality
 */

#pragma once

#include <sys/cdefs.h>

#include "Types.h"

__BEGIN_DECLS

/**
 * Get the version of Neuron runtime library.
 * @note Neuron runtime can only load DLA files generated by compiler with the same major version.
 * @param version the version of Neuron runtime library.
 * @return A RuntimeAPI error code.
 */
int NeuronRuntime_getVersion(NeuronVersion* version);

__END_DECLS
