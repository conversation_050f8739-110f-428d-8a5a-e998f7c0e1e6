# NeuroPilot 配置
macro(CONFIG_NEUROPILOT)
    if(CONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT)
        if(CPPBUILD_TARGET_OS STREQUAL "ANDROID")
            # NeuroPilot配置 for Android - 使用mt6897平台
            set(NEUROPILOT_PATH ${PROJECT_SOURCE_DIR}/3dpart/plateform/neuropilot7/mt6897)
        else()
            message(WARNING "NeuroPilot is currently only supported on Android platform")
            return()
        endif()

        # 检查NeuroPilot库文件是否存在
        set(NEUROPILOT_ADAPTER_LIB_PATH ${NEUROPILOT_PATH}/lib/libneuron_adapter.so)
        set(NEUROPILOT_RUNTIME_LIB_PATH ${NEUROPILOT_PATH}/lib/libneuron_runtime.so)
        
        if(NOT EXISTS ${NEUROPILOT_ADAPTER_LIB_PATH})
            message(FATAL_ERROR "Could not find NeuroPilot adapter library at ${NEUROPILOT_ADAPTER_LIB_PATH}")
        endif()
        
        if(NOT EXISTS ${NEUROPILOT_RUNTIME_LIB_PATH})
            message(FATAL_ERROR "Could not find NeuroPilot runtime library at ${NEUROPILOT_RUNTIME_LIB_PATH}")
        endif()

        # 设置NeuroPilot库和依赖的Android系统库
        set(NEUROPILOT_LIB
            ${NEUROPILOT_ADAPTER_LIB_PATH}
            ${NEUROPILOT_RUNTIME_LIB_PATH}
            android       # Android library (includes nativewindow for API 21)
            m             # Math library
            dl            # Dynamic linking library
            z             # Compression library
            log           # Android log library
        )

        # 添加编译定义
        add_definitions(-DCONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT=1)

        # 包含目录
        include_directories(${NEUROPILOT_PATH}/include/)
        include_directories(${NEUROPILOT_PATH}/include/neuron/api/)
        link_directories(${NEUROPILOT_PATH}/lib/)
        
        message(STATUS "NeuroPilot configured for Android platform")
        message(STATUS "NeuroPilot include path: ${NEUROPILOT_PATH}/include/")
        message(STATUS "NeuroPilot library path: ${NEUROPILOT_PATH}/lib/")
    endif()
endmacro()

# NeuroPilot 安装配置
macro(INSTALL_NEUROPILOT)
    if(CONFIG_AUTO_ENGINE_ENABLE_NEUROPILOT)
        if(CPPBUILD_IS_TOPLEVEL OR (NOT CPPBUILD_REMOTE))
            if(NEUROPILOT_PATH)
                # 安装主要的库文件
                CPPBUILD_INSTALL(FILES ${NEUROPILOT_PATH}/lib/libneuron_adapter.so DESTINATION ${DST_LIB_DIR})
                CPPBUILD_INSTALL(FILES ${NEUROPILOT_PATH}/lib/libneuron_runtime.so DESTINATION ${DST_LIB_DIR})
                
                # 安装版本化的库文件
                CPPBUILD_INSTALL(FILES ${NEUROPILOT_PATH}/lib/libneuron_adapter.so.7 DESTINATION ${DST_LIB_DIR})
                CPPBUILD_INSTALL(FILES ${NEUROPILOT_PATH}/lib/libneuron_runtime.so.7 DESTINATION ${DST_LIB_DIR})
                CPPBUILD_INSTALL(FILES ${NEUROPILOT_PATH}/lib/libneuron_adapter.so.7.3.15 DESTINATION ${DST_LIB_DIR})
                CPPBUILD_INSTALL(FILES ${NEUROPILOT_PATH}/lib/libneuron_runtime.so.7.3.15 DESTINATION ${DST_LIB_DIR})
                
                # 安装MVPU相关库文件
                file(GLOB MVPU_LIBS "${NEUROPILOT_PATH}/lib/libmvpu*.so")
                foreach(MVPU_LIB ${MVPU_LIBS})
                    CPPBUILD_INSTALL(FILES ${MVPU_LIB} DESTINATION ${DST_LIB_DIR})
                endforeach()
                
                message(STATUS "NeuroPilot libraries will be installed to ${DST_LIB_DIR}")
            endif()
        endif()
    endif()
endmacro()
