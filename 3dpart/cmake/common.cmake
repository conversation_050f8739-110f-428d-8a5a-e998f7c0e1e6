# 通用CMake配置文件
# 用于包含所有平台的配置

# 包含各个平台的CMake文件
include(${CMAKE_CURRENT_LIST_DIR}/ppl3.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/onnx.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/ncnn.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/snpe.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/rknn.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/tensorrt.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/qnn.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/sdnn.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/mnn.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/neuropilot.cmake)


# 初始化平台变量
macro(INIT_PLATEFORM_VARS)
    
    # 初始化变量
    set(MODEL_PATH )
    set(PPL3_PATH )
    set(ONNX_PATH )
    set(NCNN_PATH )
    set(ONNX_LIB )
    set(EXTRA_LIB )
    set(SNPE_PATH )
    set(SNPE_LIB )
    set(RKNN_LIB )
    set(NCNN_LIB )
    set(TENSORRT_PATH )
    set(TENSORRT_LIB )
    set(CUDART_LIB )
    set(SDNN_PATH )
    set(SDNN_LIB )
    set(NEUROPILOT_PATH )
    set(NEUROPILOT_LIB )
endmacro()

# 配置所有平台
macro(CONFIG_ALL_PLATEFORMS)
    # 调用各平台的配置宏
    CONFIG_PPL3()
    CONFIG_ONNX()
    CONFIG_NCNN()
    CONFIG_SNPE()
    CONFIG_RKNN()
    CONFIG_TENSORRT()
    CONFIG_QNN()
    CONFIG_SDNN()
    CONFIG_MNN()
    CONFIG_NEUROPILOT()
endmacro()

# 安装所有平台的文件
macro(INSTALL_ALL_PLATEFORMS)
    INSTALL_PPL3()
    INSTALL_ONNX()
    INSTALL_NCNN()
    INSTALL_SNPE()
    INSTALL_RKNN()
    INSTALL_TENSORRT()
    INSTALL_QNN()
    INSTALL_SDNN()
    INSTALL_MNN()
    INSTALL_NEUROPILOT()
endmacro() 